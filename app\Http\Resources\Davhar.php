<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class <PERSON>vhar extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'     => $this->id,
            'number' => $this->number,
            'order'  => $this->order,
            'code'   => $this->code,
            'orc'    => $this->orc ? new Orc($this->orc) : null,
        ];
    }
}
