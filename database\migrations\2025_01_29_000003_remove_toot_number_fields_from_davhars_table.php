<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('davhars', function (Blueprint $table) {
            $table->dropColumn(['begin_toot_number', 'end_toot_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('davhars', function (Blueprint $table) {
            $table->integer('begin_toot_number')->default(0);
            $table->integer('end_toot_number')->default(0);
        });
    }
};
