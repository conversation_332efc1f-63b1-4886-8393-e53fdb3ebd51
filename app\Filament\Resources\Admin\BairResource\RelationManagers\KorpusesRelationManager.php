<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Korpus;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class KorpusesRelationManager extends RelationManager
{
    protected static string $relationship = 'korpuses';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $bairId = $this->getOwnerRecord()->id;
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->required(),
                        Forms\Components\TextInput::make(Korpus::ORDER)
                            ->required()
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $bairId = $this->getOwnerRecord()->id;
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->label('Дараалал')
                            ->minValue(1)
                            ->numeric()
                            ->maxValue(9999),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Korpus::ORDER)->label('Дараалал')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orcs_count')->counts('orcs')->label('Орц тоо'),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоот тоо'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
