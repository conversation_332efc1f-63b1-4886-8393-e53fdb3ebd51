<?php

namespace App\Filament\Resources\Admin\OrcResource\RelationManagers;

use App\Models\Davhar;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class DavharsRelationManager extends RelationManager
{
    protected static string $relationship = 'davhars';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(Davhar::NUMBER)
                            ->label('Давхарын дугаар')
                            ->required(),
                        Forms\Components\TextInput::make(Davhar::ORDER)
                            ->label('Дараалал')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $orcId = $this->getOwnerRecord()->id;
                                    return $rule->where('orc_id', $orcId);
                                }
                            )
                            ->numeric()
                            ->minValue(1)
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)->label('Давхарын дугаар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Davhar::ORDER)->label('Дараалал')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоот тоо'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
