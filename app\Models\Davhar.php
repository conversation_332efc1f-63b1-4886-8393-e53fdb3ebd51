<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Davhar
 *
 * @property int $id
 * @property int $orc_id
 * @property string $number
 * @property int $order
 * @property int $begin_toot_number
 * @property int $end_toot_number
 * @property string|null $code
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Orc $orc
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar query()
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereOrcId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereBeginTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereEndTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Davhar whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Davhar extends Model
{
    use HasFactory;

    const ID     = 'id';
    const ORC_ID = 'orc_id';
    const NUMBER = 'number';
    const ORDER  = 'order';
    const CODE   = 'code';

    const RELATION_ORC   = 'orc';
    const RELATION_TOOTS = 'toots';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::ORC_ID,
        self::NUMBER,
        self::ORDER,
        self::CODE
    ];

    public function orc(): BelongsTo
    {
        return $this->belongsTo(Orc::class);
    }

    public function toots(): HasMany
    {
        return $this->hasMany(Toot::class)->orderBy('number', 'asc');
    }
}
